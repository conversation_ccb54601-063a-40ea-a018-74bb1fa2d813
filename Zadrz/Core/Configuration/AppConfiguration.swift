//
//  AppConfiguration.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation

// MARK: - App Configuration
enum AppConfiguration {
    
    // MARK: - API Keys
    enum APIKeys {
        static var openAI: String {
            return "********************************************************************************************************************************************************************"
        }
    }
    
    // MARK: - Feature Flags
    enum Features {
        static let enableStreaming = true
        static let enableVoiceMessages = false
        static let enableFileAttachments = false
    }
    
    // MARK: - Token Limits
    enum Tokens {
        static let defaultFreeTokens = 1000
        static let warningThreshold = 100
    }
}
