//
//  TokenManager.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation
import Firebase

// MARK: - Token Manager
@MainActor
final class TokenManager: ObservableObject {
    @Published var remainingTokens: Int = 10000
    @Published var isSubscribed: Bool = false
    @Published var isTokenLimitReached: Bool = false

    private let totalTokens = 10000 // Total tokens per user across all heroes
    private let tokensPerHero = 1000 // Display limit per hero (from total pool)
    private let database = Database.database().reference()

    // MARK: - Token Operations
    func loadUserTokens(userId: String) async {
        guard !userId.isEmpty else {
            print("❌ TokenManager: Cannot load tokens - userId is empty")
            return
        }

        do {
            // Load user's total remaining tokens from Firebase
            let snapshot = try await database
                .child("users")
                .child(userId)
                .child("tokens")
                .getData()

            if let tokenData = snapshot.value as? [String: Any] {
                let totalRemaining = tokenData["totalRemaining"] as? Int ?? totalTokens
                let subscribed = tokenData["isSubscribed"] as? Bool ?? false

                await MainActor.run {
                    self.remainingTokens = totalRemaining
                    self.isSubscribed = subscribed
                    self.updateTokenLimitStatus()
                }

                #if DEBUG
                print("✅ TokenManager: Loaded tokens - Remaining: \(totalRemaining), Subscribed: \(subscribed)")
                #endif
            } else {
                // First time user - initialize with full tokens
                await initializeUserTokens(userId: userId)
            }
        } catch {
            print("❌ TokenManager: Error loading tokens: \(error)")
            // Fallback to default values
            await MainActor.run {
                self.remainingTokens = totalTokens
                self.isSubscribed = false
                self.updateTokenLimitStatus()
            }
        }
    }

    private func initializeUserTokens(userId: String) async {
        let tokenData: [String: Any] = [
            "totalRemaining": totalTokens,
            "isSubscribed": false,
            "lastUpdated": ServerValue.timestamp()
        ]

        do {
            try await database
                .child("users")
                .child(userId)
                .child("tokens")
                .setValue(tokenData)

            await MainActor.run {
                self.remainingTokens = totalTokens
                self.isSubscribed = false
                self.updateTokenLimitStatus()
            }

            #if DEBUG
            print("✅ TokenManager: Initialized user tokens with \(totalTokens) tokens")
            #endif
        } catch {
            print("❌ TokenManager: Error initializing tokens: \(error)")
        }
    }

    func consumeTokens(userId: String, tokensUsed: Int) async {
        guard !isSubscribed else {
            #if DEBUG
            print("🔄 TokenManager: Skipping token consumption - user is subscribed")
            #endif
            return
        }

        guard !userId.isEmpty, tokensUsed > 0 else {
            print("❌ TokenManager: Invalid parameters for token consumption")
            return
        }

        let newRemainingTokens = max(0, remainingTokens - tokensUsed)

        do {
            // Update in Firebase
            try await database
                .child("users")
                .child(userId)
                .child("tokens")
                .child("totalRemaining")
                .setValue(newRemainingTokens)

            try await database
                .child("users")
                .child(userId)
                .child("tokens")
                .child("lastUpdated")
                .setValue(ServerValue.timestamp())

            // Update local state
            await MainActor.run {
                self.remainingTokens = newRemainingTokens
                self.updateTokenLimitStatus()
            }

            #if DEBUG
            print("✅ TokenManager: Consumed \(tokensUsed) tokens. Remaining: \(newRemainingTokens)")
            #endif

        } catch {
            print("❌ TokenManager: Error consuming tokens: \(error)")
        }
    }

    func canSendMessage() -> Bool {
        let canSend = isSubscribed || remainingTokens > 0

        #if DEBUG
        print("🔍 TokenManager: Can send message: \(canSend) (Subscribed: \(isSubscribed), Remaining: \(remainingTokens))")
        #endif

        return canSend
    }

    /// Returns the display tokens for a specific hero (max 1000 from total pool)
    func getDisplayTokensForHero() -> Int {
        if isSubscribed {
            return tokensPerHero // Show full hero allocation for subscribed users
        }
        return min(remainingTokens, tokensPerHero)
    }

    private func updateTokenLimitStatus() {
        isTokenLimitReached = !isSubscribed && remainingTokens <= 0
    }

    // MARK: - Subscription Management
    func setSubscriptionStatus(userId: String, isSubscribed: Bool) async {
        guard !userId.isEmpty else {
            print("❌ TokenManager: Cannot set subscription - userId is empty")
            return
        }

        do {
            try await database
                .child("users")
                .child(userId)
                .child("tokens")
                .child("isSubscribed")
                .setValue(isSubscribed)

            try await database
                .child("users")
                .child(userId)
                .child("tokens")
                .child("lastUpdated")
                .setValue(ServerValue.timestamp())

            await MainActor.run {
                self.isSubscribed = isSubscribed
                self.updateTokenLimitStatus()
            }

            #if DEBUG
            print("✅ TokenManager: Updated subscription status to: \(isSubscribed)")
            #endif

        } catch {
            print("❌ TokenManager: Error updating subscription: \(error)")
        }
    }
}
