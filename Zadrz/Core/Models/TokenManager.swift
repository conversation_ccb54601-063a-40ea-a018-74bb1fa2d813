//
//  TokenManager.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation
import Firebase

// MARK: - Token Manager
@MainActor
final class TokenManager: ObservableObject {
    @Published var remainingTokens: Int = 1000
    @Published var isSubscribed: Bool = false
    @Published var isTokenLimitReached: Bool = false
    
    private let defaultTokens = 1000
    
    // MARK: - Token Operations
    func loadUserTokens(userId: String) async {
        // TODO: Implement Firestore loading when properly configured
        // For now, use local storage
        remainingTokens = defaultTokens
        isSubscribed = false
        updateTokenLimitStatus()
    }
    
    func consumeTokens(userId: String, tokensUsed: Int) async {
        guard !isSubscribed else { return } // Subscribed users have unlimited tokens
        
        remainingTokens = max(0, remainingTokens - tokensUsed)
        updateTokenLimitStatus()
        
        // TODO: Save to Firestore when properly configured
    }
    
    func canSendMessage() -> Bool {
        return isSubscribed || remainingTokens > 0
    }
    
    private func updateTokenLimitStatus() {
        isTokenLimitReached = !isSubscribed && remainingTokens <= 0
    }
    
    // MARK: - Subscription Management
    func setSubscriptionStatus(userId: String, isSubscribed: Bool) async {
        self.isSubscribed = isSubscribed
        updateTokenLimitStatus()
        
        // TODO: Save to Firestore when properly configured
    }
}
