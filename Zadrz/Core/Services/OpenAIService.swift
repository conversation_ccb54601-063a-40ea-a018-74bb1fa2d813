//
//  OpenAIService.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation

// MARK: - OpenAI Service
@MainActor
final class OpenAIService: ObservableObject {
    @Published var isLoading = false
    @Published var error: String?
    
    private let apiKey = AppConfiguration.APIKeys.openAI
    
    init() {
        // Initialize with your API key
    }
    
    // MARK: - Chat Completion with Real OpenAI API
    func sendMessage(
        messages: [ChatMessage],
        hero: HeroPersona
    ) async throws -> ChatMessage {
        isLoading = true
        defer { isLoading = false }
        
        error = nil
        
        // Prepare the conversation context for OpenAI
        var openAIMessages: [[String: Any]] = []
        
        // Add system message for hero persona
        openAIMessages.append([
            "role": "system",
            "content": hero.systemPrompt
        ])
        
        // Add conversation history (only user and assistant messages)
        for message in messages {
            if message.messageType != .date {
                let role = message.isFromUser ? "user" : "assistant"
                openAIMessages.append([
                    "role": role,
                    "content": message.text
                ])
            }
        }
        
        let requestBody: [String: Any] = [
            "model": "gpt-4o",
            "messages": openAIMessages,
            "temperature": 0.7,
            "max_tokens": 1000
        ]
        
        do {
            let response = try await makeOpenAIRequest(requestBody: requestBody)
            
            guard let choices = response["choices"] as? [[String: Any]],
                  let firstChoice = choices.first,
                  let message = firstChoice["message"] as? [String: Any],
                  let content = message["content"] as? String else {
                throw OpenAIError.noResponse
            }
            
            let aiMessage = ChatMessage(
                text: content,
                isFromUser: false,
                timestamp: getCurrentTimestamp()
            )
            
            return aiMessage
        } catch {
            self.error = error.localizedDescription
            throw error
        }
    }
    
    // MARK: - Streaming Chat with Real OpenAI API
    func sendMessageStream(
        messages: [ChatMessage],
        hero: HeroPersona
    ) -> AsyncThrowingStream<String, Error> {
        return AsyncThrowingStream { continuation in
            Task { @MainActor in
                isLoading = true
                defer { isLoading = false }
                
                error = nil
                
                // Prepare the conversation context for OpenAI
                var openAIMessages: [[String: Any]] = []
                
                // Add system message for hero persona
                openAIMessages.append([
                    "role": "system",
                    "content": hero.systemPrompt
                ])
                
                // Add conversation history
                for message in messages {
                    if message.messageType != .date {
                        let role = message.isFromUser ? "user" : "assistant"
                        openAIMessages.append([
                            "role": role,
                            "content": message.text
                        ])
                    }
                }
                
                let requestBody: [String: Any] = [
                    "model": "gpt-4o",
                    "messages": openAIMessages,
                    "temperature": 0.7,
                    "max_tokens": 1000,
                    "stream": true
                ]
                
                do {
                    _ = try await makeStreamingOpenAIRequest(requestBody: requestBody) { partialResponse in
                        continuation.yield(partialResponse)
                    }
                    
                    continuation.finish()
                } catch {
                    self.error = error.localizedDescription
                    continuation.finish(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Private Methods
    private func makeOpenAIRequest(requestBody: [String: Any]) async throws -> [String: Any] {
        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw OpenAIError.invalidConfiguration
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let error = errorData["error"] as? [String: Any],
               let message = error["message"] as? String {
                throw OpenAIError.apiError(message)
            }
            throw OpenAIError.invalidResponse
        }
        
        guard let result = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw OpenAIError.invalidResponse
        }
        
        return result
    }
    
    private func makeStreamingOpenAIRequest(
        requestBody: [String: Any],
        onPartialResponse: @escaping (String) -> Void
    ) async throws -> String {
        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            throw OpenAIError.invalidConfiguration
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw OpenAIError.invalidResponse
        }
        
        var fullResponse = ""
        let dataString = String(data: data, encoding: .utf8) ?? ""
        let lines = dataString.components(separatedBy: .newlines)
        
        for line in lines {
            if line.hasPrefix("data: ") {
                let jsonString = String(line.dropFirst(6))
                if jsonString == "[DONE]" { break }
                
                if let jsonData = jsonString.data(using: .utf8),
                   let json = try? JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
                   let choices = json["choices"] as? [[String: Any]],
                   let firstChoice = choices.first,
                   let delta = firstChoice["delta"] as? [String: Any],
                   let content = delta["content"] as? String {
                    
                    fullResponse += content
                    onPartialResponse(fullResponse)
                }
            }
        }
        
        return fullResponse
    }
    
    private func getCurrentTimestamp() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: Date())
    }
}

// MARK: - OpenAI Error
enum OpenAIError: LocalizedError {
    case noResponse
    case invalidConfiguration
    case invalidResponse
    case apiError(String)
    
    var errorDescription: String? {
        switch self {
        case .noResponse:
            return "No response received from AI"
        case .invalidConfiguration:
            return "Invalid OpenAI configuration"
        case .invalidResponse:
            return "Invalid response from OpenAI"
        case .apiError(let message):
            return "OpenAI API Error: \(message)"
        }
    }
}
