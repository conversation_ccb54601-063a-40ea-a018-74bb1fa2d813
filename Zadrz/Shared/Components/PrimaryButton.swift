//
//  PrimaryButton.swift
//  Zadrz
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

/// A customizable primary button component with support for icons, custom colors, and loading states.
struct PrimaryButton: View {
    // MARK: - Properties
    let title: String
    let icon: String?
    let backgroundColor: Color
    let foregroundColor: Color
    let isLoading: Bool
    let isEnabled: Bool
    let action: () -> Void

    // MARK: - Initializers

    /// Creates a primary button with default styling
    init(
        title: String,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = nil
        self.backgroundColor = .accentColor
        self.foregroundColor = .black
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.action = action
    }

    /// Creates a primary button with custom styling and optional icon
    init(
        title: String,
        icon: String? = nil,
        backgroundColor: Color = .accentColor,
        foregroundColor: Color = .black,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.action = action
    }

    // MARK: - Body
    var body: some View {
        Button(action: action) {
            buttonContent
                .buttonStyle(
                    backgroundColor: backgroundColor,
                    foregroundColor: foregroundColor,
                    isEnabled: isEnabled && !isLoading
                )
        }
        .disabled(!isEnabled || isLoading)
    }

    // MARK: - Private Views
    @ViewBuilder
    private var buttonContent: some View {
        if isLoading {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: foregroundColor))
                .scaleEffect(0.8)
        } else if let icon = icon {
            Label(title, systemImage: icon)
        } else {
            Text(title)
        }
    }
}

/// A customizable secondary button component with support for icons, custom colors, and loading states.
struct SecondaryButton: View {
    // MARK: - Properties
    let title: String
    let icon: String?
    let backgroundColor: Color
    let foregroundColor: Color
    let borderColor: Color
    let isLoading: Bool
    let isEnabled: Bool
    let action: () -> Void

    // MARK: - Initializers

    /// Creates a secondary button with default styling
    init(
        title: String,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = nil
        self.backgroundColor = .clear
        self.foregroundColor = .accentColor
        self.borderColor = .accentColor
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.action = action
    }

    /// Creates a secondary button with custom styling and optional icon
    init(
        title: String,
        icon: String? = nil,
        backgroundColor: Color = .clear,
        foregroundColor: Color = .accentColor,
        borderColor: Color? = nil,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.borderColor = borderColor ?? foregroundColor
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.action = action
    }

    // MARK: - Body
    var body: some View {
        Button(action: action) {
            buttonContent
                .buttonStyle(
                    backgroundColor: backgroundColor,
                    foregroundColor: foregroundColor,
                    isEnabled: isEnabled && !isLoading
                )
                .overlay(
                    RoundedRectangle(cornerRadius: AppConstants.UI.buttonHeight / 2)
                        .stroke(borderColor, lineWidth: 2)
                        .opacity((isEnabled && !isLoading) ? 1.0 : 0.5)
                )
        }
        .disabled(!isEnabled || isLoading)
    }

    // MARK: - Private Views
    @ViewBuilder
    private var buttonContent: some View {
        if isLoading {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: foregroundColor))
                .scaleEffect(0.8)
        } else if let icon = icon {
            Label(title, systemImage: icon)
        } else {
            Text(title)
        }
    }
}

#Preview {
    ScrollView {
        VStack(spacing: 20) {
            Text("Primary Buttons")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Default primary button
            PrimaryButton(title: "Default Primary") {
                print("Default primary tapped")
            }

            // Primary with icon
            PrimaryButton(
                title: "With Icon",
                icon: "star.fill"
            ) {
                print("Icon primary tapped")
            }

            // Custom colored primary
            PrimaryButton(
                title: "Custom Colors",
                icon: "heart.fill",
                backgroundColor: .red,
                foregroundColor: .white
            ) {
                print("Custom primary tapped")
            }

            // Loading state
            PrimaryButton(title: "Loading", isLoading: true) {
                print("Loading tapped")
            }

            Divider()

            Text("Secondary Buttons")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Default secondary button
            SecondaryButton(title: "Default Secondary") {
                print("Default secondary tapped")
            }

            // Secondary with icon
            SecondaryButton(
                title: "With Icon",
                icon: "plus.circle"
            ) {
                print("Icon secondary tapped")
            }

            // Custom colored secondary
            SecondaryButton(
                title: "Custom Colors",
                icon: "trash",
                backgroundColor: .red.opacity(0.1),
                foregroundColor: .red,
                borderColor: .red
            ) {
                print("Custom secondary tapped")
            }

            // Disabled state
            SecondaryButton(title: "Disabled", isEnabled: false) {
                print("Disabled tapped")
            }
        }
        .padding()
    }
}
